"""
نموذج العميل - Customer Model
"""

from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional
import pandas as pd


@dataclass
class Customer:
    """نموذج العميل"""
    name: str
    phone: Optional[str] = None
    email: Optional[str] = None
    address: Optional[str] = None
    notes: Optional[str] = None
    created_date: Optional[str] = None
    
    def __post_init__(self):
        if self.created_date is None:
            self.created_date = datetime.now().strftime("%Y-%m-%d")
    
    def to_dict(self) -> dict:
        """تحويل العميل إلى قاموس"""
        return {
            'name': self.name,
            'phone': self.phone,
            'email': self.email,
            'address': self.address,
            'notes': self.notes,
            'created_date': self.created_date
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'Customer':
        """إنشاء عميل من قاموس"""
        return cls(
            name=data['name'],
            phone=data.get('phone'),
            email=data.get('email'),
            address=data.get('address'),
            notes=data.get('notes'),
            created_date=data.get('created_date')
        )


class CustomerManager:
    """مدير العملاء"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.columns = [
            "name", "phone", "email", "address", "notes", "created_date"
        ]
    
    def save_customer(self, customer: Customer) -> bool:
        """حفظ عميل جديد"""
        try:
            import os
            
            # تحميل العملاء الحاليين
            df = self.load_customers()
            
            # التحقق من عدم وجود العميل مسبقاً
            if not df.empty and customer.name in df['name'].values:
                return False  # العميل موجود مسبقاً
            
            # إضافة العميل الجديد
            new_customer_df = pd.DataFrame([customer.to_dict()])
            
            if df.empty:
                new_customer_df.to_csv(self.file_path, index=False, encoding='utf-8-sig')
            else:
                df = pd.concat([df, new_customer_df], ignore_index=True)
                df.to_csv(self.file_path, index=False, encoding='utf-8-sig')
            
            return True
        except Exception as e:
            print(f"خطأ في حفظ العميل: {str(e)}")
            return False
    
    def load_customers(self) -> pd.DataFrame:
        """تحميل جميع العملاء"""
        try:
            import os
            
            if os.path.isfile(self.file_path):
                return pd.read_csv(self.file_path, encoding='utf-8-sig')
            else:
                return pd.DataFrame(columns=self.columns)
        except Exception as e:
            print(f"خطأ في تحميل العملاء: {str(e)}")
            return pd.DataFrame(columns=self.columns)
    
    def get_customer(self, name: str) -> Optional[Customer]:
        """الحصول على عميل محدد"""
        df = self.load_customers()
        if not df.empty:
            customer_data = df[df['name'] == name]
            if not customer_data.empty:
                return Customer.from_dict(customer_data.iloc[0].to_dict())
        return None
    
    def update_customer(self, customer: Customer) -> bool:
        """تحديث بيانات عميل"""
        try:
            df = self.load_customers()
            if not df.empty:
                # البحث عن العميل
                customer_index = df[df['name'] == customer.name].index
                if not customer_index.empty:
                    # تحديث البيانات
                    for key, value in customer.to_dict().items():
                        df.loc[customer_index[0], key] = value
                    
                    df.to_csv(self.file_path, index=False, encoding='utf-8-sig')
                    return True
            return False
        except Exception as e:
            print(f"خطأ في تحديث العميل: {str(e)}")
            return False
    
    def delete_customer(self, name: str) -> bool:
        """حذف عميل"""
        try:
            df = self.load_customers()
            if not df.empty:
                df = df[df['name'] != name]
                df.to_csv(self.file_path, index=False, encoding='utf-8-sig')
                return True
            return False
        except Exception as e:
            print(f"خطأ في حذف العميل: {str(e)}")
            return False
    
    def search_customers(self, query: str) -> pd.DataFrame:
        """البحث في العملاء"""
        df = self.load_customers()
        if not df.empty and query:
            # البحث في الاسم والهاتف والبريد الإلكتروني
            mask = (
                df['name'].str.contains(query, case=False, na=False) |
                df['phone'].str.contains(query, case=False, na=False) |
                df['email'].str.contains(query, case=False, na=False)
            )
            return df[mask]
        return df
    
    def get_customer_statistics(self) -> dict:
        """الحصول على إحصائيات العملاء"""
        df = self.load_customers()
        
        if df.empty:
            return {
                'total_customers': 0,
                'customers_with_phone': 0,
                'customers_with_email': 0,
                'recent_customers': 0
            }
        
        # العملاء الجدد في آخر 30 يوم
        recent_date = (datetime.now() - pd.Timedelta(days=30)).strftime("%Y-%m-%d")
        recent_customers = df[df['created_date'] >= recent_date] if 'created_date' in df.columns else pd.DataFrame()
        
        return {
            'total_customers': len(df),
            'customers_with_phone': df['phone'].notna().sum(),
            'customers_with_email': df['email'].notna().sum(),
            'recent_customers': len(recent_customers)
        }
