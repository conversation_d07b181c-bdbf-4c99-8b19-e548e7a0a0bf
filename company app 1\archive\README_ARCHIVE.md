# 📁 مجلد الأرشيف - Archive Folder

## 📋 نظرة عامة

هذا المجلد يحتوي على الملفات المرجعية والإصدارات القديمة من المشروع التي لم تعد مستخدمة في النسخة الحالية، ولكن تم الاحتفاظ بها كمرجع للتطوير المستقبلي أو لأغراض التوثيق.

## 📄 الملفات المحفوظة

### 1. `app.py`
- **الوصف:** الملف الأصلي الأول للمشروع
- **التاريخ:** الإصدار الأولي
- **السبب في الأرشفة:** تم تطويره وتحسينه إلى `invoice_app.py`
- **المحتوى:** نظام فواتير بسيط بدون صفحات متعددة
- **الحالة:** مُستبدل ✅

### 2. `main_app.py`
- **الوصف:** النسخة المحسنة الثانية من التطبيق
- **التاريخ:** الإصدار المتوسط
- **السبب في الأرشفة:** تم دمجه في `invoice_app.py` مع إضافات جديدة
- **المحتوى:** نظام فواتير مع بعض التحسينات
- **الحالة:** مُستبدل ✅

### 3. `01_بيانات_العملاء.py`
- **الوصف:** صفحة حسابات العملاء المنفصلة
- **التاريخ:** تم إنشاؤها كصفحة منفصلة
- **السبب في الأرشفة:** تم دمجها في `invoice_app.py` كصفحة داخلية
- **المحتوى:** كشف حساب العملاء مع رسوم بيانية
- **الحالة:** مُدمجة في الملف الرئيسي ✅

### 4. `main.py`
- **الوصف:** الملف الرئيسي للبنية الجديدة المنظمة
- **التاريخ:** تم إنشاؤه كجزء من إعادة التنظيم الاستراتيجية
- **السبب في الأرشفة:** مخصص للمستقبل عندما تكتمل البنية الجديدة
- **المحتوى:** نقطة دخول منظمة مع إعدادات مركزية
- **الحالة:** للاستخدام المستقبلي ⏳

## 🎯 الغرض من الأرشفة

### ✅ **الفوائد:**
- **الحفاظ على التاريخ:** تتبع تطور المشروع
- **المرجعية:** الرجوع للكود القديم عند الحاجة
- **التوثيق:** فهم القرارات التطويرية
- **النسخ الاحتياطية:** حماية من فقدان الكود

### 🔄 **متى نستخدم هذه الملفات:**
- عند الحاجة لمراجعة الكود القديم
- لفهم كيفية تطور المشروع
- عند الرغبة في استخراج جزء معين من الكود
- للمقارنة بين الإصدارات المختلفة

## 📊 مقارنة الإصدارات

| الملف | الميزات | الصفحات | الحالة |
|-------|---------|---------|--------|
| `app.py` | أساسية | 1 | مُستبدل |
| `main_app.py` | محسنة | متعددة | مُستبدل |
| `01_بيانات_العملاء.py` | متخصصة | 1 | مُدمجة |
| `main.py` | منظمة | 1 | مستقبلية |
| **`invoice_app.py`** | **شاملة** | **6** | **نشط** ✅ |

## 🚀 الملف الحالي النشط

**الملف الرئيسي الحالي:** `invoice_app.py`

### المميزات الحالية:
- ✅ 6 صفحات متكاملة
- ✅ نظام تنقل موحد
- ✅ جميع الوظائف مدمجة
- ✅ واجهة محسنة
- ✅ رسوم بيانية تفاعلية
- ✅ إدارة شاملة للبيانات

## 📝 ملاحظات للمطورين

### 🔧 **عند التطوير:**
- لا تعدل الملفات في هذا المجلد
- استخدم `invoice_app.py` للتطوير الجديد
- ارجع لهذه الملفات للمرجعية فقط

### 📚 **للتعلم:**
- راجع تطور الكود من `app.py` إلى `invoice_app.py`
- لاحظ كيف تم تحسين البنية والوظائف
- استفد من الأفكار القديمة للتطوير المستقبلي

## 🗂️ بنية المجلد

```
archive/
├── README_ARCHIVE.md          # هذا الملف
├── app.py                     # الإصدار الأول
├── main_app.py               # الإصدار المتوسط
└── 01_بيانات_العملاء.py      # الصفحة المنفصلة القديمة
```

## ⚠️ تحذيرات

- **لا تشغل** هذه الملفات في البيئة الحالية
- **لا تعدل** محتويات هذا المجلد
- **استخدم للمرجعية فقط**

## 📞 للاستفسارات

إذا كنت بحاجة لاستخراج جزء من الكود القديم أو فهم تطور معين، راجع هذه الملفات أو اتصل بفريق التطوير.

---

**تاريخ الأرشفة:** يناير 2024  
**آخر تحديث:** يناير 2024  
**المسؤول:** فريق Crestal Diamond
