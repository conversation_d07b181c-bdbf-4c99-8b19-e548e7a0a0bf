# 💎 نظام إدارة ورشة Crestal Diamond

## 🏗️ البنية الجديدة المنظمة

تم إعادة تنظيم المشروع بطريقة استراتيجية ومهنية لتحضيره للتوسع المستقبلي.

## 🎯 الملف الرئيسي النشط

**`invoice_app.py`** هو الملف الرئيسي الوحيد المستخدم حالياً ويحتوي على:
- ✅ **6 صفحات كاملة** ومجربة
- ✅ **جميع الوظائف** متاحة ومتكاملة
- ✅ **واجهة موحدة** مع تنقل سهل
- ✅ **يعمل بدون مشاكل** أو تعقيدات

**الملفات الأخرى** (في مجلدات src/, config/, tests/) مخصصة للاستخدام المستقبلي عند التوسع.

## 📁 هيكل المشروع

```
company app 1/
├── 🎯 invoice_app.py             # الملف الرئيسي النشط (6 صفحات)
├── 🔍 excel_analyzer.py          # محلل ملفات Excel المنفصل
├── 🚀 run.bat                    # سكريبت التشغيل السريع
├── 📁 src/                       # كود منظم (للمستقبل)
│   ├── 📁 core/                  # الوظائف الأساسية
│   │   ├── __init__.py
│   │   ├── database.py           # مدير قاعدة البيانات
│   │   ├── excel_handler.py      # معالج ملفات Excel
│   │   └── utils.py              # الأدوات المساعدة
│   ├── 📁 models/                # نماذج البيانات
│   │   ├── __init__.py
│   │   ├── invoice.py            # نموذج الفاتورة
│   │   ├── customer.py           # نموذج العميل
│   │   └── excel_analyzer.py     # نموذج تحليل Excel
│   ├── 📁 pages/                 # صفحات التطبيق
│   │   ├── __init__.py
│   │   ├── invoice_creation.py   # صفحة إنشاء الفواتير
│   │   ├── invoice_list.py       # صفحة عرض الفواتير
│   │   ├── customer_accounts.py  # صفحة حسابات العملاء
│   │   ├── excel_analysis.py     # صفحة تحليل Excel
│   │   ├── reports.py            # صفحة التقارير
│   │   └── settings.py           # صفحة الإعدادات
│   └── 📁 utils/                 # الأدوات المساعدة
│       ├── __init__.py
│       ├── formatters.py         # أدوات التنسيق
│       └── validators.py         # أدوات التحقق
├── 📁 data/                      # البيانات المنظمة
│   ├── 📁 invoices/              # ملفات الفواتير
│   │   └── invoices.csv
│   ├── 📁 customers/             # بيانات العملاء
│   │   └── customers.csv
│   ├── 📁 exports/               # الملفات المصدرة
│   └── 📁 backups/               # النسخ الاحتياطية
├── 📁 config/                    # إعدادات (للمستقبل)
│   ├── settings.py               # إعدادات التطبيق
│   └── requirements.txt          # المكتبات المطلوبة
├── 📁 docs/                      # التوثيق
│   ├── README.md                 # هذا الملف
│   ├── README_EN.md              # دليل إنجليزي
│   ├── CHANGELOG.md              # سجل التغييرات
│   ├── DEVELOPMENT.md            # دليل المطورين
│   └── LICENSE                   # الرخصة
├── 📁 scripts/                   # سكريبتات التشغيل
│   ├── install_requirements.bat  # تثبيت المكتبات
│   ├── run_main.bat              # تشغيل التطبيق الرئيسي
│   └── run_all_apps.bat          # تشغيل جميع التطبيقات
├── 📁 tests/                     # اختبارات (للمستقبل)
├── 📁 assets/                    # الموارد (صور، أيقونات)
├── 📁 logs/                      # ملفات السجلات
└── 📁 archive/                   # الملفات المرجعية
    ├── README_ARCHIVE.md         # دليل الأرشيف
    ├── app.py                    # الإصدار الأول
    ├── main_app.py               # الإصدار المتوسط
    ├── 01_بيانات_العملاء.py      # الصفحة المنفصلة
    └── main.py                   # البنية المستقبلية
```

## 🚀 التشغيل

### الطريقة الأساسية (موصى بها):
```bash
streamlit run invoice_app.py
```

### أو باستخدام السكريبت السريع:
```bash
# Windows
run.bat
```

### أو باستخدام السكريبتات المتقدمة:
```bash
# Windows
scripts\run_main.bat
scripts\run_all_apps.bat
```

## ✨ المميزات الجديدة

### 🏗️ **البنية المنظمة:**
- **فصل الاهتمامات:** كل وظيفة في ملف منفصل
- **قابلية التوسع:** سهولة إضافة مميزات جديدة
- **سهولة الصيانة:** كود منظم وواضح
- **إعادة الاستخدام:** مكونات قابلة للاستخدام المتكرر

### 📊 **إدارة البيانات المحسنة:**
- **مدير قاعدة بيانات موحد:** `DatabaseManager`
- **نماذج بيانات منظمة:** `Invoice`, `Customer`
- **نسخ احتياطية تلقائية**
- **تصدير واستيراد محسن**

### ⚙️ **إعدادات مركزية:**
- **ملف إعدادات شامل:** `config/settings.py`
- **مسارات منظمة ومرنة**
- **إعدادات قابلة للتخصيص**
- **رسائل نظام موحدة**

### 🔧 **أدوات تطوير:**
- **اختبارات منظمة:** مجلد `tests/`
- **سجلات منظمة:** مجلد `logs/`
- **أرشيف منظم:** مجلد `archive/`
- **توثيق شامل:** مجلد `docs/`

## 📦 التثبيت

### 1. تثبيت المكتبات:
```bash
pip install -r config/requirements.txt
```

### 2. تشغيل التطبيق:
```bash
streamlit run invoice_app.py
```

## 🎯 الصفحات المتاحة (في invoice_app.py)

1. **📄 إنشاء فاتورة جديدة** - إنشاء فواتير تفاعلية مع حساب الذهب والأحجار
2. **📊 عرض الفواتير المحفوظة** - إدارة ومراجعة الفواتير مع البحث والفلترة
3. **👥 حسابات العملاء** - كشوف حساب مفصلة مع رسوم بيانية تفاعلية
4. **🔍 تحليل ملفات Excel** - تحليل البيانات الخارجية من ملفات Excel و CSV
5. **📈 إحصائيات وتقارير** - تقارير تفاعلية ومؤشرات الأداء
6. **⚙️ الإعدادات** - إدارة النظام والنسخ الاحتياطية

## 🔧 للمطورين

### إضافة صفحة جديدة:
1. إنشاء ملف في `src/pages/`
2. إضافة الصفحة في `main.py`
3. تحديث التوثيق

### إضافة نموذج بيانات جديد:
1. إنشاء ملف في `src/models/`
2. تحديث `__init__.py`
3. إضافة الوظائف في `DatabaseManager`

### تخصيص الإعدادات:
- تعديل `config/settings.py`
- إضافة متغيرات بيئة جديدة
- تحديث المسارات حسب الحاجة

## 📈 المستقبل

هذه البنية الجديدة تدعم:
- **قواعد بيانات متقدمة** (PostgreSQL, MySQL)
- **واجهات برمجة التطبيقات** (REST APIs)
- **تطبيقات جوال** مصاحبة
- **تكامل مع أنظمة خارجية**
- **نشر سحابي** متقدم

## 📞 الدعم

للدعم الفني أو الاستفسارات:
- **البريد الإلكتروني:** <EMAIL>
- **التوثيق:** `docs/`
- **المشاكل:** GitHub Issues

---

**تم التطوير بواسطة:** فريق Crestal Diamond  
**الإصدار:** 3.0.0 (البنية الجديدة)  
**آخر تحديث:** يناير 2024
