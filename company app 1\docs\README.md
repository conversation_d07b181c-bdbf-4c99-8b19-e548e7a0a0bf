# 💎 نظام إدارة ورشة Crestal Diamond

## 📋 نظرة عامة

نظام إدارة شامل لورش الذهب والمجوهرات يوفر حلول متكاملة لإدارة الفواتير، تحليل البيانات، والتقارير المالية. تم تطويره باستخدام Python و Streamlit لتوفير واجهة مستخدم تفاعلية وسهلة الاستخدام.

## ✨ المميزات الرئيسية

### 📄 نظام الفواتير
- إنشاء فواتير تفاعلية للعملاء
- حساب تكاليف الذهب والمصنعية
- إدارة الأحجار الكريمة (من العميل أو الورشة)
- دعم العملتين (الدولار الأمريكي والجنيه المصري)
- خدمات إضافية (بانيو، دمغة، تصليح)
- حفظ تلقائي للفواتير في ملفات CSV

### 📊 إدارة البيانات
- عرض جميع الفواتير المحفوظة
- البحث والفلترة حسب العميل والتاريخ
- إحصائيات مالية شاملة
- تصدير البيانات إلى Excel
- نسخ احتياطية للبيانات

### 👥 حسابات العملاء
- كشف حساب مفصل لكل عميل
- عرض الأرصدة النهائية (ذهب، دولار، جنيه)
- رسوم بيانية لتطور المبيعات واستهلاك الذهب
- فلترة العمليات حسب التاريخ
- تصدير بيانات العميل
- قائمة شاملة بجميع العملاء مع الإحصائيات

### 🔍 تحليل ملفات Excel
- قراءة وتحليل ملفات Excel و CSV
- معاينة محتويات الملفات
- عرض إحصائيات الملفات (صفوف، أعمدة، أوراق)
- تصدير البيانات بتنسيقات مختلفة
- معالجة الأخطاء والملفات التالفة

### 📈 التقارير والإحصائيات
- رسوم بيانية تفاعلية للمبيعات
- تحليل أداء العملاء
- إحصائيات شهرية ومالية
- تقارير استهلاك الذهب
- مؤشرات الأداء الرئيسية

## 🛠️ التقنيات المستخدمة

- **Python 3.8+** - لغة البرمجة الأساسية
- **Streamlit** - إطار عمل واجهة المستخدم
- **Pandas** - معالجة وتحليل البيانات
- **Plotly** - الرسوم البيانية التفاعلية
- **OpenPyXL** - قراءة ملفات Excel
- **SQLAlchemy** - إدارة قواعد البيانات

## 📦 التثبيت والإعداد

### المتطلبات الأساسية
```bash
Python 3.8 أو أحدث
pip (مدير الحزم)
```

### خطوات التثبيت

1. **استنساخ المشروع:**
```bash
git clone [repository-url]
cd "company app 1"
```

2. **تثبيت المكتبات المطلوبة:**
```bash
pip install -r requirements.txt
```

أو التثبيت اليدوي:
```bash
pip install streamlit pandas openpyxl xlrd plotly matplotlib seaborn sqlalchemy
```

3. **تشغيل التطبيق:**
```bash
streamlit run invoice_app.py
```

## 🚀 كيفية الاستخدام

### تشغيل التطبيق
```bash
# التطبيق الرئيسي المحسن
streamlit run invoice_app.py

# صفحة حسابات العملاء
streamlit run pages/01_بيانات_العملاء.py

# محلل ملفات Excel المنفصل
streamlit run excel_analyzer.py

# التطبيق الأصلي البسيط
streamlit run main_app.py

# تشغيل جميع التطبيقات معاً (Windows)
run_all_apps.bat
```

### الوصول للتطبيق
افتح المتصفح وانتقل إلى: `http://localhost:8501`

## 📱 دليل المستخدم

### 📄 إنشاء فاتورة جديدة
1. اختر "📄 إنشاء فاتورة جديدة" من القائمة الجانبية
2. أدخل معلومات العميل والتاريخ
3. احسب تكاليف الذهب والمصنعية
4. أضف تكاليف الأحجار والخدمات الإضافية
5. راجع الملخص النهائي واحفظ الفاتورة

### 📊 عرض الفواتير
1. اختر "📊 عرض الفواتير المحفوظة"
2. استخدم البحث والفلترة حسب الحاجة
3. راجع الإحصائيات السريعة
4. صدر البيانات أو احذف الفواتير

### 🔍 تحليل ملفات Excel
1. اختر "🔍 تحليل ملفات Excel"
2. أدخل مسار المجلد المحتوي على الملفات
3. اضغط "تحليل الملفات"
4. راجع النتائج والإحصائيات
5. استخدم خيارات التصدير والاستيراد

### 📈 التقارير والإحصائيات
1. اختر "📈 إحصائيات وتقارير"
2. راجع الإحصائيات العامة
3. تفاعل مع الرسوم البيانية
4. راجع جدول أفضل العملاء

## 📁 هيكل المشروع

```
company app 1/
├── invoice_app.py          # 🎯 الملف الرئيسي - قلب المشروع
├── excel_analyzer.py       # 🔍 محلل ملفات Excel المنفصل
├── archive/               # 📁 مجلد الملفات المرجعية
│   ├── README_ARCHIVE.md  # دليل الأرشيف
│   ├── app.py            # الإصدار الأول (مُستبدل)
│   ├── main_app.py       # الإصدار المتوسط (مُستبدل)
│   └── 01_بيانات_العملاء.py # الصفحة المنفصلة القديمة (مُدمجة)
├── requirements.txt       # قائمة المكتبات المطلوبة
├── install_requirements.bat # سكريبت تثبيت المكتبات
├── run_all_apps.bat      # تشغيل جميع التطبيقات
├── invoices.csv          # ملف الفواتير المحفوظة (يتم إنشاؤه تلقائياً)
├── README.md             # هذا الملف
├── README_EN.md          # دليل المستخدم (إنجليزي)
├── CHANGELOG.md          # سجل التغييرات
├── DEVELOPMENT.md        # دليل المطورين
├── LICENSE               # رخصة المشروع
└── .gitignore           # ملفات Git المستبعدة
```

## 💾 إدارة البيانات

### ملفات البيانات
- **`invoices.csv`** - يحتوي على جميع الفواتير المحفوظة
- **نسخ احتياطية** - يمكن إنشاؤها من صفحة الإعدادات

### تنسيق البيانات
```csv
customer_name,date,description,gold_change,usd_change,egp_change,timestamp
أحمد محمد,2024-01-15,خاتم ذهب,5.2,150.00,75.50,2024-01-15 14:30:25
```

## 🔧 الإعدادات والتخصيص

### تغيير المسارات الافتراضية
في ملف `invoice_app.py`، يمكن تعديل:
```python
default_path = r"C:\Users\<USER>\OneDrive\Desktop\crestal diamond"
```

### إضافة عملات جديدة
يمكن توسيع النظام لدعم عملات إضافية من خلال تعديل دوال الحساب.

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**خطأ في تثبيت المكتبات:**
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

**خطأ في قراءة ملفات Excel:**
- تأكد من صحة مسار الملف
- تحقق من أن الملف غير مفتوح في برنامج آخر
- جرب تحويل الملف إلى CSV

**خطأ في حفظ البيانات:**
- تأكد من وجود صلاحيات الكتابة في المجلد
- تحقق من مساحة القرص الصلب

## 🔄 التحديثات المستقبلية

### المميزات المخططة
- [ ] قاعدة بيانات متقدمة (PostgreSQL/MySQL)
- [ ] نظام مصادقة المستخدمين
- [ ] تطبيق جوال مصاحب
- [ ] تكامل مع أنظمة المحاسبة
- [ ] تقارير PDF قابلة للطباعة
- [ ] نظام إنذارات المخزون
- [ ] واجهة برمجة تطبيقات (API)

## 👥 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. تطبيق التغييرات مع التوثيق
4. إرسال Pull Request

## 📞 الدعم والتواصل

للدعم الفني أو الاستفسارات:
- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +20 XXX XXX XXXX
- **الموقع:** www.crestal-diamond.com

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للتفاصيل.

## 🙏 شكر وتقدير

- فريق تطوير Crestal Diamond
- مجتمع Streamlit
- مجتمع Python العربي

---

**تم التطوير بواسطة:** فريق Crestal Diamond  
**آخر تحديث:** يناير 2024  
**الإصدار:** 2.0
