"""
نموذج الفاتورة - Invoice Model
"""

from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional
import pandas as pd


@dataclass
class InvoiceItem:
    """عنصر في الفاتورة"""
    description: str
    quantity: float
    unit_price: float
    currency: str
    total: float = 0.0
    
    def __post_init__(self):
        self.total = self.quantity * self.unit_price


@dataclass
class Invoice:
    """نموذج الفاتورة"""
    customer_name: str
    date: str
    description: str
    gold_change: float
    usd_change: float
    egp_change: float
    timestamp: Optional[str] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def to_dict(self) -> dict:
        """تحويل الفاتورة إلى قاموس"""
        return {
            'customer_name': self.customer_name,
            'date': self.date,
            'description': self.description,
            'gold_change': self.gold_change,
            'usd_change': self.usd_change,
            'egp_change': self.egp_change,
            'timestamp': self.timestamp
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'Invoice':
        """إنشاء فاتورة من قاموس"""
        return cls(
            customer_name=data['customer_name'],
            date=data['date'],
            description=data['description'],
            gold_change=data['gold_change'],
            usd_change=data['usd_change'],
            egp_change=data['egp_change'],
            timestamp=data.get('timestamp')
        )
    
    def to_dataframe(self) -> pd.DataFrame:
        """تحويل الفاتورة إلى DataFrame"""
        return pd.DataFrame([self.to_dict()])


class InvoiceManager:
    """مدير الفواتير"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.columns = [
            "customer_name", "date", "description", 
            "gold_change", "usd_change", "egp_change", "timestamp"
        ]
    
    def save_invoice(self, invoice: Invoice) -> bool:
        """حفظ فاتورة جديدة"""
        try:
            import os
            
            # إنشاء DataFrame للفاتورة الجديدة
            new_data = invoice.to_dataframe()
            
            # التحقق من وجود الملف
            if not os.path.isfile(self.file_path):
                # إنشاء ملف جديد
                new_data.to_csv(self.file_path, index=False, encoding='utf-8-sig')
            else:
                # إضافة إلى الملف الموجود
                new_data.to_csv(self.file_path, mode='a', header=False, 
                               index=False, encoding='utf-8-sig')
            
            return True
        except Exception as e:
            print(f"خطأ في حفظ الفاتورة: {str(e)}")
            return False
    
    def load_invoices(self) -> pd.DataFrame:
        """تحميل جميع الفواتير"""
        try:
            import os
            
            if os.path.isfile(self.file_path):
                return pd.read_csv(self.file_path, encoding='utf-8-sig')
            else:
                return pd.DataFrame(columns=self.columns)
        except Exception as e:
            print(f"خطأ في تحميل الفواتير: {str(e)}")
            return pd.DataFrame(columns=self.columns)
    
    def get_customer_invoices(self, customer_name: str) -> pd.DataFrame:
        """الحصول على فواتير عميل محدد"""
        df = self.load_invoices()
        if not df.empty:
            return df[df['customer_name'] == customer_name]
        return pd.DataFrame(columns=self.columns)
    
    def delete_invoice(self, index: int) -> bool:
        """حذف فاتورة"""
        try:
            df = self.load_invoices()
            if not df.empty and index < len(df):
                df = df.drop(index).reset_index(drop=True)
                df.to_csv(self.file_path, index=False, encoding='utf-8-sig')
                return True
            return False
        except Exception as e:
            print(f"خطأ في حذف الفاتورة: {str(e)}")
            return False
    
    def get_statistics(self) -> dict:
        """الحصول على إحصائيات الفواتير"""
        df = self.load_invoices()
        
        if df.empty:
            return {
                'total_invoices': 0,
                'total_customers': 0,
                'total_usd': 0.0,
                'total_egp': 0.0,
                'total_gold': 0.0
            }
        
        return {
            'total_invoices': len(df),
            'total_customers': df['customer_name'].nunique(),
            'total_usd': df['usd_change'].sum(),
            'total_egp': df['egp_change'].sum(),
            'total_gold': abs(df['gold_change'].sum())
        }
